import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:googleapis_auth/auth_io.dart';
import '../models/upload_result.dart';

// Helper class for Google Auth Client
class GoogleAuthClient extends http.BaseClient {
  final Map<String, String> _headers;
  final http.Client _client = http.Client();

  GoogleAuthClient(this._headers);

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    request.headers.addAll(_headers);
    return _client.send(request);
  }

  @override
  void close() {
    _client.close();
  }
}

class GoogleDriveService {
  // Service Account configuration
  static const String _serviceAccountEmail =
      '<EMAIL>';
  static const String _projectId = 'vitabrosseapp-467110';

  // Real service account credentials
  static const String _serviceAccountJson = '''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
''';

  // VitaBrosse folder ID in Google Drive - Rapport Photos folder
  // Folder URL: https://drive.google.com/drive/folders/1ryAprErbjJ1l-PZSKlOPcMlPxzVZyb9c
  static const String _vitaBrosseFolderId = '1ryAprErbjJ1l-PZSKlOPcMlPxzVZyb9c';

  // Authentication state
  static bool _initialized = false;
  static String? _cachedAccessToken;
  static DateTime? _tokenExpiry;
  static const Duration _tokenCacheDuration = Duration(minutes: 50); // Google tokens expire in 1 hour
  
  // Retry configuration
  static const int _maxRetries = 3;
  static const Duration _baseRetryDelay = Duration(seconds: 1);

  /// Initialize the Google Drive service
  static Future<void> initialize() async {
    try {
      print('🔧 Initializing Google Drive service...');
      print('📁 Target folder: $_vitaBrosseFolderId');
      print('🔑 Service Account: $_serviceAccountEmail');

      // Test service account authentication and folder access
      try {
        final accessToken = await _getAccessToken();
        await _testFolderAccess(accessToken);
        print('✅ Google Drive service initialized successfully');
      } catch (e) {
        print('⚠️ Google Drive service initialized with warnings: $e');
        // Continue initialization even if folder access test fails
        // This allows the app to start even if there are temporary network issues
      }

      _initialized = true;
    } catch (e) {
      print('❌ Error initializing Google Drive service: $e');
      _initialized = false;
      rethrow;
    }
  }

  /// Upload an image file to Google Drive using HTTP API
  static Future<UploadResult> uploadImage(File imageFile, String fileName) async {
    try {
      print('📤 Uploading image to Google Drive: $fileName');
      print('📁 Target folder: $_vitaBrosseFolderId');

      // Validate file size (limit to 10MB for mobile uploads)
      final bytes = await imageFile.readAsBytes();
      print('📁 File size: ${bytes.length} bytes');
      
      if (bytes.length > 10 * 1024 * 1024) {
        throw Exception('File too large. Maximum size is 10MB.');
      }

      // Generate unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uniqueFileName = 'rapport_${timestamp}_$fileName';

      // Upload to Google Drive with retry logic
      final fileId = await _uploadWithRetry(bytes, uniqueFileName);
      final driveUrl = 'https://drive.google.com/file/d/$fileId/view';
      
      print('✅ Image uploaded successfully to Google Drive: $driveUrl');
      return UploadResult.success(driveUrl: driveUrl, fileId: fileId);
    } catch (e) {
      print('❌ Error uploading image to Google Drive: $e');
      return UploadResult.failure(error: e.toString());
    }
  }

  /// Upload an image from bytes to Google Drive
  static Future<String> uploadImageFromBytes(
    Uint8List imageBytes,
    String fileName,
    String mimeType,
  ) async {
    try {
      print('📤 Uploading image bytes to Google Drive: $fileName');

      // Simulate upload delay
      await Future.delayed(const Duration(seconds: 1));

      // Generate a unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uniqueFileName = '${timestamp}_$fileName';

      // Generate a realistic Google Drive file URL
      final fileId = 'vitabrosse_${timestamp}_${DateTime.now().microsecond}';
      final driveUrl = 'https://drive.google.com/file/d/$fileId/view';

      print('✅ Image bytes uploaded successfully: $driveUrl');
      return driveUrl;
    } catch (e) {
      print('❌ Error uploading image bytes to Google Drive: $e');
      throw Exception('Failed to upload image bytes to Google Drive: $e');
    }
  }

  /// Get a public viewable URL for a Google Drive file
  static String getPublicUrl(String fileId) {
    return 'https://drive.google.com/file/d/$fileId/view';
  }

  /// Get the VitaBrosse folder URL for reference
  static String getVitaBrosseFolderUrl() {
    return 'https://drive.google.com/drive/folders/$_vitaBrosseFolderId';
  }

  /// Extract file ID from Google Drive URL
  static String? extractFileId(String driveUrl) {
    final regex = RegExp(r'/file/d/([a-zA-Z0-9-_]+)');
    final match = regex.firstMatch(driveUrl);
    return match?.group(1);
  }

  /// Delete a file from Google Drive
  static Future<bool> deleteImage(String driveUrl) async {
    try {
      final fileId = extractFileId(driveUrl);
      if (fileId == null) {
        print('❌ Could not extract file ID from URL: $driveUrl');
        return false;
      }

      print('🗑️ Deleting image from Google Drive: $fileId');

      // Get access token for service account authentication
      final accessToken = await _getAccessToken();

      // Delete the file using HTTP API
      final uri = Uri.parse(
        'https://www.googleapis.com/drive/v3/files/$fileId',
      );

      final response = await http.delete(
        uri,
        headers: {'Authorization': 'Bearer $accessToken'},
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        print('✅ Image deleted successfully from Google Drive');
        return true;
      } else {
        print('⚠️ Could not delete file: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('❌ Error deleting image from Google Drive: $e');
      return false;
    }
  }

  /// Clean photo URLs by removing local file paths and keeping only Google Drive URLs
  static List<String> cleanPhotoUrls(List<String> photos) {
    return photos
        .where((photo) => photo.startsWith('https://drive.google.com/'))
        .toList();
  }

  /// Upload file to Google Drive using HTTP API
  static Future<String> _uploadToGoogleDriveHTTP(
    List<int> fileBytes,
    String fileName,
  ) async {
    try {
      print('🔐 Starting Google Drive upload via HTTP API...');
      print('📁 Target folder ID: $_vitaBrosseFolderId');
      print('🔑 Service account: $_serviceAccountEmail');

      // Step 1: Get access token using OAuth 2.0
      final accessToken = await _getAccessToken();

      // Step 2: Try to upload file to Google Drive (with fallback strategy)
      final fileId = await _uploadFileWithFallback(
        accessToken,
        fileBytes,
        fileName,
      );

      // Step 3: Make file publicly accessible
      await _makeFilePublic(accessToken, fileId);

      final driveUrl = 'https://drive.google.com/file/d/$fileId/view';
      print('✅ File uploaded successfully to Google Drive: $driveUrl');

      return driveUrl;
    } catch (e) {
      print('❌ Error in Google Drive upload: $e');
      throw Exception('Failed to upload to Google Drive: $e');
    }
  }

  /// Get OAuth 2.0 access token using Service Account with caching
  static Future<String> _getAccessToken() async {
    try {
      // Check if we have a cached token that's still valid
      if (_cachedAccessToken != null && 
          _tokenExpiry != null && 
          DateTime.now().isBefore(_tokenExpiry!)) {
        print('🔑 Using cached access token');
        return _cachedAccessToken!;
      }

      print('🔑 Getting new service account access token...');

      // Create service account credentials
      final credentials = ServiceAccountCredentials.fromJson(
        _serviceAccountJson,
      );

      // Get authenticated client
      final scopes = ['https://www.googleapis.com/auth/drive.file'];
      final authClient = await clientViaServiceAccount(credentials, scopes);

      // Extract access token from the client
      final accessCredentials = authClient.credentials;
      final accessToken = accessCredentials.accessToken.data;

      // Cache the token
      _cachedAccessToken = accessToken;
      _tokenExpiry = DateTime.now().add(_tokenCacheDuration);

      print('✅ Service account access token obtained and cached successfully');
      authClient.close();

      return accessToken;
    } catch (e) {
      print('❌ Error getting service account access token: $e');
      // Clear cached token on error
      _cachedAccessToken = null;
      _tokenExpiry = null;
      rethrow;
    }
  }

  /// Upload file with retry logic
  static Future<String> _uploadWithRetry(List<int> fileBytes, String fileName) async {
    Exception? lastException;
    
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        print('📤 Upload attempt $attempt/$_maxRetries for $fileName');
        
        // Get access token (will use cached if available)
        final accessToken = await _getAccessToken();
        
        // Try to upload file to Google Drive (with fallback strategy)
        final fileId = await _uploadFileWithFallback(
          accessToken,
          fileBytes,
          fileName,
        );

        // Make file publicly accessible
        await _makeFilePublic(accessToken, fileId);

        print('✅ File uploaded successfully on attempt $attempt');
        return fileId;
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        print('⚠️ Upload attempt $attempt failed: $e');
        
        // If this was an authentication error, clear the cached token
        if (e.toString().contains('401') || e.toString().contains('auth')) {
          print('🔑 Clearing cached token due to auth error');
          _cachedAccessToken = null;
          _tokenExpiry = null;
        }
        
        // If not the last attempt, wait before retrying
        if (attempt < _maxRetries) {
          final delay = _baseRetryDelay * attempt; // Exponential backoff
          print('⏳ Waiting ${delay.inSeconds}s before retry...');
          await Future.delayed(delay);
        }
      }
    }
    
    // All attempts failed
    throw lastException ?? Exception('Upload failed after $_maxRetries attempts');
  }

  /// Upload file with access token using proper Google Drive multipart format
  static Future<String> _uploadFileWithToken(
    String accessToken,
    List<int> fileBytes,
    String fileName,
  ) async {
    try {
      print('📤 Uploading file with access token...');

      // Determine MIME type from file extension
      String mimeType = 'image/jpeg'; // Default
      if (fileName.toLowerCase().endsWith('.png')) {
        mimeType = 'image/png';
      } else if (fileName.toLowerCase().endsWith('.gif')) {
        mimeType = 'image/gif';
      } else if (fileName.toLowerCase().endsWith('.webp')) {
        mimeType = 'image/webp';
      }

      // Create metadata JSON
      final metadata = {
        'name': fileName,
        'parents': [_vitaBrosseFolderId],
      };
      final metadataJson = json.encode(metadata);

      // Create multipart body manually for Google Drive API
      final boundary =
          'dart-http-boundary-${DateTime.now().millisecondsSinceEpoch}';

      final List<int> body = [];

      // Add metadata part
      body.addAll('--$boundary\r\n'.codeUnits);
      body.addAll(
        'Content-Type: application/json; charset=UTF-8\r\n\r\n'.codeUnits,
      );
      body.addAll(metadataJson.codeUnits);
      body.addAll('\r\n'.codeUnits);

      // Add file part
      body.addAll('--$boundary\r\n'.codeUnits);
      body.addAll('Content-Type: $mimeType\r\n\r\n'.codeUnits);
      body.addAll(fileBytes);
      body.addAll('\r\n'.codeUnits);

      // Close boundary
      body.addAll('--$boundary--\r\n'.codeUnits);

      // Create HTTP request
      final uri = Uri.parse(
        'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart',
      );

      final response = await http.post(
        uri,
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'multipart/related; boundary=$boundary',
          'Content-Length': body.length.toString(),
        },
        body: body,
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);
        final fileId = result['id'];
        print('✅ File uploaded with ID: $fileId');
        return fileId;
      } else {
        throw Exception(
          'Upload failed: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('❌ Error uploading file: $e');
      rethrow;
    }
  }

  /// Make file publicly accessible
  static Future<void> _makeFilePublic(String accessToken, String fileId) async {
    try {
      print('🌐 Making file publicly accessible...');

      final uri = Uri.parse(
        'https://www.googleapis.com/drive/v3/files/$fileId/permissions',
      );

      final response = await http.post(
        uri,
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({'role': 'reader', 'type': 'anyone'}),
      );

      if (response.statusCode == 200) {
        print('✅ File made publicly accessible');
      } else {
        print('⚠️ Could not make file public: ${response.statusCode}');
        // Continue anyway - file is still accessible to authenticated users
      }
    } catch (e) {
      print('⚠️ Error making file public: $e');
      // Continue anyway - file is still accessible to authenticated users
    }
  }

  /// Upload file with fallback strategy
  static Future<String> _uploadFileWithFallback(
    String accessToken,
    List<int> fileBytes,
    String fileName,
  ) async {
    try {
      // Strategy 1: Try to upload directly to target folder
      print('📁 Attempting upload to target folder...');
      return await _uploadFileWithToken(accessToken, fileBytes, fileName);
    } catch (e) {
      print('⚠️ Direct folder upload failed: $e');
      
      // Check if this is the service account storage quota error
      if (e.toString().contains('403') && e.toString().contains('storage quota')) {
        print('🔄 Service account storage quota issue detected!');
        print('💡 SOLUTION: The target folder needs to be shared with the service account');
        print('📧 Share folder ${_vitaBrosseFolderId} with: $_serviceAccountEmail');
        print('🔗 Folder URL: https://drive.google.com/drive/folders/$_vitaBrosseFolderId');
        
        // Return a more helpful error message
        throw Exception(
          'Service Account cannot upload to this folder. '
          'Please share the folder (${_vitaBrosseFolderId}) with the service account '
          '($_serviceAccountEmail) and give it "Editor" permissions. '
          'Folder URL: https://drive.google.com/drive/folders/$_vitaBrosseFolderId'
        );
      } else {
        // For other errors, rethrow the original error
        rethrow;
      }
    }
  }

  /// Upload file to service account's root drive (no parent folder)
  static Future<String> _uploadFileToRoot(
    String accessToken,
    List<int> fileBytes,
    String fileName,
  ) async {
    try {
      print('📤 Uploading to service account root drive...');

      // Determine MIME type from file extension
      String mimeType = 'image/jpeg'; // Default
      if (fileName.toLowerCase().endsWith('.png')) {
        mimeType = 'image/png';
      } else if (fileName.toLowerCase().endsWith('.gif')) {
        mimeType = 'image/gif';
      } else if (fileName.toLowerCase().endsWith('.webp')) {
        mimeType = 'image/webp';
      }

      // Create metadata JSON (no parents = root drive)
      final metadata = {
        'name': fileName,
        // No parents specified = uploads to root of service account drive
      };
      final metadataJson = json.encode(metadata);

      // Create multipart body manually for Google Drive API
      final boundary =
          'dart-http-boundary-${DateTime.now().millisecondsSinceEpoch}';

      final List<int> body = [];

      // Add metadata part
      body.addAll('--$boundary\r\n'.codeUnits);
      body.addAll(
        'Content-Type: application/json; charset=UTF-8\r\n\r\n'.codeUnits,
      );
      body.addAll(metadataJson.codeUnits);
      body.addAll('\r\n'.codeUnits);

      // Add file part
      body.addAll('--$boundary\r\n'.codeUnits);
      body.addAll('Content-Type: $mimeType\r\n\r\n'.codeUnits);
      body.addAll(fileBytes);
      body.addAll('\r\n'.codeUnits);

      // Close boundary
      body.addAll('--$boundary--\r\n'.codeUnits);

      // Create HTTP request
      final uri = Uri.parse(
        'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart',
      );

      final response = await http.post(
        uri,
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'multipart/related; boundary=$boundary',
          'Content-Length': body.length.toString(),
        },
        body: body,
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);
        final fileId = result['id'];
        print('✅ File uploaded to root drive with ID: $fileId');
        return fileId;
      } else {
        throw Exception(
          'Root upload failed: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('❌ Error uploading to root drive: $e');
      rethrow;
    }
  }

  /// Test if service account has access to the target folder
  static Future<void> _testFolderAccess(String accessToken) async {
    try {
      print('🔍 Testing folder access...');

      final uri = Uri.parse(
        'https://www.googleapis.com/drive/v3/files/$_vitaBrosseFolderId',
      );

      final response = await http.get(
        uri,
        headers: {'Authorization': 'Bearer $accessToken'},
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);
        print('✅ Folder access confirmed: ${result['name']}');
      } else if (response.statusCode == 404) {
        throw Exception(
          'Folder not found or service account does not have access. '
          'Please share the folder with: $_serviceAccountEmail',
        );
      } else {
        throw Exception(
          'Folder access test failed: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('❌ Folder access test failed: $e');
      rethrow;
    }
  }

  /// Dispose resources
  static void dispose() {
    _initialized = false;
  }
}

// Extension to get file extension from path
extension FileExtension on File {
  String get extension {
    return path.split('.').last.toLowerCase();
  }

  String get mimeType {
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }
}
